import os
import time
from flask import Blueprint, jsonify, request
from datetime import datetime
from firebase_config import get_db
from image_data import get_signed_url
import google.cloud.logging
from utils import get_client_ip

# Initialize Google Cloud Logging client
client = google.cloud.logging.Client()
client.setup_logging()
logger = client.logger('verse_view_api_v2')

v2_bp = Blueprint('v2', __name__)

API_KEY = os.environ.get("EXTENSION_API_KEY")

def get_verse_from_firebase(date_str, username="default"):
    """
    Get verse data from Firebase for a specific date and username
    
    Args:
        date_str (str): Date in YYYY-MM-DD format
        username (str): Username for year mapping (defaults to "default")
    
    Returns:
        dict: Verse data with reference, text, and image info
    """
    try:
        # Parse the date string and format as DD-MM for lookup
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        lookup_key = date_obj.strftime('%d-%m')
        
        # Get Firestore database
        db = get_db()
        
        # First, get the day document to find the verse ID
        day_ref = db.collection('days').document(lookup_key)
        day_doc = day_ref.get()
        
        if not day_doc.exists:
            return None
            
        day_data = day_doc.to_dict()
        
        # Try to get verse ID for the specific username/year, fallback to "default"
        verse_id = None
        if username in day_data:
            verse_id = day_data[username].get('verseId')
        elif 'default' in day_data:
            verse_id = day_data['default'].get('verseId')
            
        if not verse_id:
            return None
            
        # Now get the verse details
        verse_ref = db.collection('verses').document(verse_id)
        verse_doc = verse_ref.get()
        
        if not verse_doc.exists:
            return None
            
        verse_data = verse_doc.to_dict()
        
        # Get the verse text from versions subcollection (prefer NIV, fallback to any available)
        versions_ref = verse_ref.collection('versions')
        versions = list(versions_ref.stream())
        
        verse_text = ""
        verse_translation = ""
        
        # Try to find NIV first, then any other version
        niv_version = None
        any_version = None
        
        for version_doc in versions:
            version_data = version_doc.to_dict()
            if version_doc.id.lower() == 'niv':
                niv_version = version_data
                break
            elif any_version is None:
                any_version = version_data
                
        if niv_version:
            verse_text = niv_version.get('text', '')
            verse_translation = niv_version.get('translation', 'NIV')
        elif any_version:
            verse_text = any_version.get('text', '')
            verse_translation = any_version.get('translation', '')
            
        # Format the reference
        book = verse_data.get('book', '')
        chapter = verse_data.get('chapter', '')
        verse_start = verse_data.get('verseStart', '')
        verse_end = verse_data.get('verseEnd', '')
        
        if verse_start == verse_end:
            reference = f"{book} {chapter}:{verse_start}"
        else:
            reference = f"{book} {chapter}:{verse_start}-{verse_end}"
            
        # Get image name
        image_name = verse_data.get('imageName', '')
        
        return {
            'reference': reference,
            'text': verse_text,
            'translation': verse_translation,
            'image_name': image_name,
            'verse_id': verse_id
        }
        
    except Exception as e:
        logger.error(f"Error getting verse from Firebase: {e}")
        return None

@v2_bp.route('/getverse', methods=['GET'])
def get_verse():
    # Validate API key
    incoming_key = request.headers.get("X-API-Key")
    if incoming_key != API_KEY:
        # Log unauthorized access attempt
        client_ip = get_client_ip()
        logger.log_text(
            f"Unauthorized API access attempt: IP={client_ip}, Key={incoming_key}",
            severity="WARNING"
        )
        return jsonify({"error": "Unauthorized"}), 401

    # Get parameters from query string
    date_str = request.args.get('date')
    username = request.args.get('username', 'default')  # Default to "default" if not provided

    if not date_str:
        # Log missing date parameter
        client_ip = get_client_ip()
        logger.log_text(
            f"Missing date parameter: IP={client_ip}",
            severity="WARNING"
        )
        return jsonify({"error": "Date parameter is required"}), 400

    try:
        # Validate date format
        datetime.strptime(date_str, '%Y-%m-%d')
        
        # Get client IP address
        client_ip = get_client_ip()

        # Log the API call to Google Cloud Logging
        logger.log_text(
            f"API call to v2/getverse: IP={client_ip}, Date={date_str}, Username={username}",
            severity="INFO"
        )

        # Get verse from Firebase
        verse_data = get_verse_from_firebase(date_str, username)

        if verse_data:
            response = {
                'reference': verse_data['reference'],
                'text': verse_data['text']
            }
            
            # Add translation info if available
            if verse_data.get('translation'):
                response['translation'] = verse_data['translation']
            
            # Generate signed URL for the image if available
            if verse_data.get('image_name'):
                try:
                    signed_url = get_signed_url(verse_data['image_name'])
                    if signed_url:
                        response['background_image'] = signed_url
                except Exception as e:
                    # If there's an error with Google Cloud, continue without the image
                    error_msg = f"Error generating signed URL: {str(e)}"
                    print(error_msg)
                    # Log the error
                    logger.log_text(
                        f"Error in v2/getverse API: IP={client_ip}, Date={date_str}, Username={username}, Error={error_msg}",
                        severity="ERROR"
                    )
            
            return jsonify(response)
        else:
            # Log the not found error
            logger.log_text(
                f"No verse found: IP={client_ip}, Date={date_str}, Username={username}",
                severity="WARNING"
            )
            return jsonify({"error": "No verse found for this date"}), 404

    except ValueError:
        # Log the invalid date format error
        client_ip = get_client_ip()
        logger.log_text(
            f"Invalid date format: IP={client_ip}, Date={date_str}",
            severity="WARNING"
        )
        return jsonify({"error": "Invalid date format. Please use YYYY-MM-DD"}), 400
    except Exception as e:
        # Log any other errors
        client_ip = get_client_ip()
        logger.log_text(
            f"Unexpected error in v2/getverse API: IP={client_ip}, Date={date_str}, Username={username}, Error={str(e)}",
            severity="ERROR"
        )
        return jsonify({"error": "Internal server error"}), 500
