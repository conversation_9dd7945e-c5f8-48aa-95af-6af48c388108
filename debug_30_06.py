#!/usr/bin/env python3
"""
Debug script to check why 30-06 isn't returning a verse
"""
import sys
import os

# Add the current directory to path so we can import from Verse_Admin
current_dir = os.path.dirname(os.path.abspath(__file__))
verse_admin_dir = os.path.join(current_dir, 'Verse_Admin')
verse_server_dir = os.path.join(current_dir, 'Verse_ViewServer')
sys.path.insert(0, verse_admin_dir)
sys.path.insert(0, verse_server_dir)

from firebase_config import get_db

def debug_30_06():
    """Debug the 30-06 date issue"""
    try:
        print("=== Debugging 30-06 Date Issue ===")
        
        # Get Firestore database
        db = get_db()
        print("✓ Connected to Firebase")
        
        # Check if the 30-06 document exists in days collection
        lookup_key = "30-06"
        print(f"\n1. Checking for document '{lookup_key}' in 'days' collection...")
        
        day_ref = db.collection('days').document(lookup_key)
        day_doc = day_ref.get()
        
        if day_doc.exists:
            print(f"✓ Document '{lookup_key}' exists!")
            day_data = day_doc.to_dict()
            print(f"Document data: {day_data}")
            
            # Check for default mapping
            if 'default' in day_data:
                verse_id = day_data['default'].get('verseId')
                print(f"✓ Found verse ID in default mapping: {verse_id}")
                
                # Now check if the verse exists
                if verse_id:
                    print(f"\n2. Checking for verse document '{verse_id}' in 'verses' collection...")
                    verse_ref = db.collection('verses').document(verse_id)
                    verse_doc = verse_ref.get()
                    
                    if verse_doc.exists:
                        print(f"✓ Verse document '{verse_id}' exists!")
                        verse_data = verse_doc.to_dict()
                        print(f"Verse data: {verse_data}")
                        
                        # Check versions subcollection
                        print(f"\n3. Checking versions subcollection for '{verse_id}'...")
                        versions_ref = verse_ref.collection('versions')
                        versions = list(versions_ref.stream())
                        
                        if versions:
                            print(f"✓ Found {len(versions)} version(s):")
                            for version_doc in versions:
                                version_data = version_doc.to_dict()
                                print(f"  - {version_doc.id}: {version_data}")
                        else:
                            print("✗ No versions found in subcollection!")
                            
                    else:
                        print(f"✗ Verse document '{verse_id}' does not exist!")
                else:
                    print("✗ No verse ID found in default mapping!")
            else:
                print("✗ No 'default' mapping found in document!")
                print("Available mappings:", list(day_data.keys()))
        else:
            print(f"✗ Document '{lookup_key}' does not exist!")
            
            # Let's check what documents do exist in the days collection
            print("\n4. Checking all documents in 'days' collection...")
            days_ref = db.collection('days')
            days = list(days_ref.stream())
            
            print(f"Found {len(days)} documents in 'days' collection:")
            june_days = []
            for day_doc in days:
                day_id = day_doc.id
                if day_id.endswith('-06'):  # June days
                    june_days.append(day_id)
            
            june_days.sort()
            print(f"June days found: {june_days}")
            
            if '30-06' not in june_days:
                print("✗ 30-06 is missing from the days collection!")
            
        # Let's check what verses do exist
        print(f"\n5. Checking existing verses in 'verses' collection...")
        verses_ref = db.collection('verses')
        verses = list(verses_ref.stream())

        print(f"Found {len(verses)} verses in collection:")
        philippians_verses = []
        for verse_doc in verses:
            verse_id = verse_doc.id
            if 'philippians' in verse_id.lower():
                philippians_verses.append(verse_id)

        if philippians_verses:
            print(f"Philippians verses found: {philippians_verses}")
        else:
            print("No Philippians verses found")

        # Check the content of the similar Philippians verses
        print(f"\n6. Checking content of similar Philippians 2:14-15 verses...")
        candidates = [' Philippians_2_14_15', 'Philippians_2_14-15']

        for candidate in candidates:
            if candidate in philippians_verses:
                print(f"\nChecking verse '{candidate}':")
                verse_ref = db.collection('verses').document(candidate)
                verse_doc = verse_ref.get()
                if verse_doc.exists:
                    verse_data = verse_doc.to_dict()
                    print(f"  Verse data: {verse_data}")

                    # Check versions
                    versions_ref = verse_ref.collection('versions')
                    versions = list(versions_ref.stream())
                    if versions:
                        print(f"  Found {len(versions)} version(s):")
                        for version_doc in versions:
                            version_data = version_doc.to_dict()
                            print(f"    - {version_doc.id}: {version_data.get('text', 'No text')[:100]}...")
                    else:
                        print("  No versions found")

        print(f"\n7. SOLUTION: Update the day mapping to point to the correct existing verse")
        print("The day document should point to one of the existing Philippians verses that has content.")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_30_06()
