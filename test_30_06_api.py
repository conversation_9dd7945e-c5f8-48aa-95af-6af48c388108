#!/usr/bin/env python3
"""
Test script to verify the 30-06 API works after the fix
"""
import sys
import os

# Add the current directory to path so we can import from Verse_Admin
current_dir = os.path.dirname(os.path.abspath(__file__))
verse_admin_dir = os.path.join(current_dir, 'Verse_Admin')
verse_server_dir = os.path.join(current_dir, 'Verse_ViewServer')
sys.path.insert(0, verse_admin_dir)
sys.path.insert(0, verse_server_dir)

from firebase_config import get_db

def test_get_verse_from_firebase(date_str, username="default"):
    """
    Test the get_verse_from_firebase function directly
    (Simplified version without Google Cloud Logging)
    """
    try:
        from datetime import datetime
        
        # Parse the date string and format as DD-MM for lookup
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        lookup_key = date_obj.strftime('%d-%m')
        
        # Get Firestore database
        db = get_db()
        
        # First, get the day document to find the verse ID
        day_ref = db.collection('days').document(lookup_key)
        day_doc = day_ref.get()
        
        if not day_doc.exists:
            return None
            
        day_data = day_doc.to_dict()
        
        # Try to get verse ID for the specific username/year, fallback to "default"
        verse_id = None
        if username in day_data:
            verse_id = day_data[username].get('verseId')
        elif 'default' in day_data:
            verse_id = day_data['default'].get('verseId')
            
        if not verse_id:
            return None
            
        # Now get the verse details
        verse_ref = db.collection('verses').document(verse_id)
        verse_doc = verse_ref.get()
        
        if not verse_doc.exists:
            return None
            
        verse_data = verse_doc.to_dict()
        
        # Get the verse text from versions subcollection (prefer NIV, fallback to any available)
        versions_ref = verse_ref.collection('versions')
        versions = list(versions_ref.stream())
        
        verse_text = ""
        verse_translation = ""
        
        # Try to find NIV first, then any other version
        niv_version = None
        any_version = None
        
        for version_doc in versions:
            version_data = version_doc.to_dict()
            if version_doc.id.lower() == 'niv':
                niv_version = version_data
                break
            elif any_version is None:
                any_version = version_data
                
        if niv_version:
            verse_text = niv_version.get('text', '')
            verse_translation = niv_version.get('translation', 'NIV')
        elif any_version:
            verse_text = any_version.get('text', '')
            verse_translation = any_version.get('translation', '')
            
        # Format the reference
        book = verse_data.get('book', '')
        chapter = verse_data.get('chapter', '')
        verse_start = verse_data.get('verseStart', '')
        verse_end = verse_data.get('verseEnd', '')
        
        if verse_start == verse_end:
            reference = f"{book} {chapter}:{verse_start}"
        else:
            reference = f"{book} {chapter}:{verse_start}-{verse_end}"
            
        # Get image name
        image_name = verse_data.get('imageName', '')
        
        return {
            'reference': reference,
            'text': verse_text,
            'translation': verse_translation,
            'image_name': image_name,
            'verse_id': verse_id
        }
        
    except Exception as e:
        print(f"Error getting verse from Firebase: {e}")
        return None

def test_30_06_api():
    """Test the 30-06 API functionality"""
    try:
        print("=== Testing 30-06 API After Fix ===")
        
        # Test the function
        result = test_get_verse_from_firebase('2024-06-30', 'default')
        
        if result:
            print("✓ SUCCESS! The API now returns a verse for 30-06:")
            print(f"  Reference: {result['reference']}")
            print(f"  Translation: {result['translation']}")
            print(f"  Text: {result['text'][:100]}...")
            print(f"  Image: {result['image_name']}")
            print(f"  Verse ID: {result['verse_id']}")
        else:
            print("✗ FAILED! The API still returns None for 30-06")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_30_06_api()
