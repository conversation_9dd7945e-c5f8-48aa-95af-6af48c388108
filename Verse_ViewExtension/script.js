// Global variables
//TEST
// const API_SERVER = 'http://localhost:5001/api/v1';

//PROD
const API_SERVER = 'https://verse-view-265611274173.us-central1.run.app/api/v2';

/*
DEBUG FEATURES (can be commented out for production):
1. Date picker in top-right corner - allows testing different dates
2. <PERSON><PERSON> can button - clears all Chrome local storage
3. Console logging - logs API calls and responses for debugging

To disable debug features:
- Comment out the date picker input in index.html (line 23)
- Comment out the trash button in index.html (lines 24-30)
- Comment out console.log statements in this file (search for "DEBUG:")
- Comment out debug CSS styles in styles.css (lines 57-100)
*/

// API Key management
const API_KEY = 's4lNDrBVgjmWhcnsGYDtydWiUx7wphLMCcfeNY_FwAM';

// Default settings
const DEFAULT_SETTINGS = {
    username: '',
    translation: '<PERSON>J<PERSON>'
};

// Settings Management Functions
async function loadSettings() {
    const result = await new Promise(resolve => {
        chrome.storage.local.get(['userSettings'], resolve);
    });
    return result.userSettings || DEFAULT_SETTINGS;
}

async function saveSettings(settings) {
    await chrome.storage.local.set({ 'userSettings': settings });
}

// Notification Functions
function showNotification(message, duration = 3000) {
    const notification = document.getElementById('notification');
    const notificationText = document.getElementById('notification-text');

    notificationText.textContent = message;
    notification.style.display = 'block';
    notification.classList.remove('fade-out');

    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.style.display = 'none';
            notification.classList.remove('fade-out');
        }, 300); // Wait for fade-out animation to complete
    }, duration);
}

// Settings UI Functions
function showSettings() {
    document.getElementById('main-container').style.display = 'none';
    document.getElementById('settings-container').style.display = 'flex';
    loadSettingsToUI();
}

function hideSettings() {
    document.getElementById('settings-container').style.display = 'none';
    document.getElementById('main-container').style.display = 'flex';
}

async function loadSettingsToUI() {
    const settings = await loadSettings();
    document.getElementById('username-input').value = settings.username || '';
    document.getElementById('translation-select').value = settings.translation || 'KJV';
}

async function saveSettingsFromUI() {
    const username = document.getElementById('username-input').value.trim();
    const translation = document.getElementById('translation-select').value;

    const settings = {
        username: username,
        translation: translation
    };

    await saveSettings(settings);

    // Clear cached verse data to force refresh with new settings
    await chrome.storage.local.remove(['verseData']);

    // Hide settings and return to main page
    hideSettings();

    // Show success notification
    showNotification('Settings Saved', 3000);

    // Refresh the verse with new settings
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;

    fetchAndDisplayVerse(dateString);
}

// Function to convert image URL to base64
async function imageUrlToBase64(url) {
    try {
        const response = await fetch(url);
        const blob = await response.blob();
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    } catch (error) {
        console.error('Error converting image to base64:', error);
        return null;
    }
}

// Function to hide loading message and show verse content
function hideLoadingMessage() {
    // Hide loading message
    const loadingMessage = document.getElementById('loading-message');
    if (loadingMessage) {
        loadingMessage.style.display = 'none';
    }

    // Show verse container
    const verseContainer = document.querySelector('.verse-container');
    if (verseContainer) {
        verseContainer.style.display = 'block';
    }
}

// Function to fetch and display verse for a specific date
async function fetchAndDisplayVerse(dateString) {
    // Show loading message (it's already visible by default)

    // Get user settings
    const settings = await loadSettings();

    // First check local storage
    const storageResult = await new Promise(resolve => {
        chrome.storage.local.get(['verseData', 'backgroundImageData'], resolve);
    });

    // If either verse data is missing or background image is missing, fetch fresh data
    if (!storageResult.verseData || !storageResult.backgroundImageData || storageResult.verseData.date !== dateString) {
        try {
            // Build API URL with username parameter if available
            let apiUrl = `${API_SERVER}/getverse?date=${dateString}`;
            if (settings.username) {
                apiUrl += `&username=${encodeURIComponent(settings.username)}`;
            }

            // DEBUG: Log the API call
            console.log('DEBUG: Making API call to:', apiUrl);
            console.log('DEBUG: Request headers:', {
                'Content-Type': 'application/json',
                'X-API-Key': API_KEY
            });

            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': API_KEY
                }
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();

            // DEBUG: Log the API response
            console.log('DEBUG: API response received:', data);

            if (data && data.text && data.reference) {
                // Store the verse data
                await chrome.storage.local.set({
                    'verseData': {
                        date: dateString,
                        text: data.text,
                        reference: data.reference,
                        translation: data.translation || ''
                    }
                });

                // Display the verse
                document.getElementById('verse-text').textContent = data.text;
                document.getElementById('verse-reference').textContent = data.reference;
                document.getElementById('verse-version').textContent = data.translation || '';

                // We'll hide the loading message after the background image loads

                // Handle background image
                if (data.background_image) {
                    try {
                        // Convert image to base64 and store it
                        const base64Image = await imageUrlToBase64(data.background_image);
                        if (base64Image) {
                            await chrome.storage.local.set({
                                'backgroundImageData': base64Image
                            });
                            document.body.style.backgroundImage = `url('${base64Image}')`;

                            // Hide loading message after background image is set
                            hideLoadingMessage();
                        } else {
                            document.body.style.backgroundImage = 'url("background.jpg")';
                            // Hide loading message even if we're using default background
                            hideLoadingMessage();
                        }
                    } catch (error) {
                        console.error('Error processing background image:', error);
                        document.body.style.backgroundImage = 'url("background.jpg")';
                        // Hide loading message even if there's an error with the image
                        hideLoadingMessage();
                    }
                } else {
                    document.body.style.backgroundImage = 'url("background.jpg")';
                    // Hide loading message even if there's no background image
                    hideLoadingMessage();
                }
            } else {
                throw new Error('Invalid response format');
            }
        } catch (error) {
            console.error('Error:', error);
            // DEBUG: Additional error logging
            console.log('DEBUG: Error details - Type:', error.name, 'Message:', error.message);
            console.log('DEBUG: Failed API URL was:', `${API_SERVER}/getverse?date=${dateString}${settings.username ? `&username=${encodeURIComponent(settings.username)}` : ''}`);

            document.getElementById('verse-text').textContent = 'Error loading verse. Please try again later.';
            document.getElementById('verse-reference').textContent = '';
            document.getElementById('verse-version').textContent = '';
            document.body.style.backgroundImage = 'url("background.jpg")';

            // Hide loading message even on error
            hideLoadingMessage();
        }
    } else {
        // We have both verse data and background image in storage
        // DEBUG: Log when using cached data
        console.log('DEBUG: Using cached data for date:', dateString);
        console.log('DEBUG: Cached verse data:', storageResult.verseData);

        document.getElementById('verse-text').textContent = storageResult.verseData.text;
        document.getElementById('verse-reference').textContent = storageResult.verseData.reference;
        document.getElementById('verse-version').textContent = storageResult.verseData.translation || '';
        document.body.style.backgroundImage = `url('${storageResult.backgroundImageData}')`;

        // Hide loading message
        hideLoadingMessage();
    }
}
// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Settings button event listener
    document.getElementById('settings-btn').addEventListener('click', showSettings);

    // Save settings button event listener
    document.getElementById('save-settings-btn').addEventListener('click', saveSettingsFromUI);

    // Cancel settings button event listener
    document.getElementById('cancel-settings-btn').addEventListener('click', hideSettings);

    // Get current date in YYYY-MM-DD format based on user's local time zone
    const today = new Date();
    // Use local date methods to get year, month, and day in user's time zone
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0'); // getMonth() is 0-indexed
    const day = String(today.getDate()).padStart(2, '0');
    // Format as YYYY-MM-DD
    const dateString = `${year}-${month}-${day}`;

    // Fetch initial verse
    fetchAndDisplayVerse(dateString);

    // DEBUG: Date picker event listener
    const datePicker = document.getElementById('datePicker');
    if (datePicker) {
        // Set the date picker to today's date
        datePicker.value = dateString;

        datePicker.addEventListener('change', function() {
            const selectedDate = this.value;
            console.log('DEBUG: Date picker changed to:', selectedDate);

            // Clear cached data to force fresh API call
            chrome.storage.local.remove(['verseData', 'backgroundImageData'], () => {
                console.log('DEBUG: Cleared cached data for date change');
                // Fetch verse for the new date
                fetchAndDisplayVerse(selectedDate);
            });
        });
    }

    // DEBUG: Clear storage button event listener
    const clearStorageBtn = document.getElementById('clear-storage-btn');
    if (clearStorageBtn) {
        clearStorageBtn.addEventListener('click', function() {
            console.log('DEBUG: Clearing all Chrome local storage...');

            chrome.storage.local.clear(() => {
                console.log('DEBUG: All Chrome local storage cleared');
                showNotification('Debug: Local storage cleared', 2000);

                // Reload the current date to fetch fresh data
                const currentDate = datePicker ? datePicker.value : dateString;
                fetchAndDisplayVerse(currentDate);
            });
        });
    }
});