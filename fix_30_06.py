#!/usr/bin/env python3
"""
Fix script to update the 30-06 day mapping to point to the correct verse
"""
import sys
import os

# Add the current directory to path so we can import from Verse_Admin
current_dir = os.path.dirname(os.path.abspath(__file__))
verse_admin_dir = os.path.join(current_dir, 'Verse_Admin')
verse_server_dir = os.path.join(current_dir, 'Verse_ViewServer')
sys.path.insert(0, verse_admin_dir)
sys.path.insert(0, verse_server_dir)

from firebase_config import get_db

def fix_30_06():
    """Fix the 30-06 date mapping"""
    try:
        print("=== Fixing 30-06 Date Mapping ===")
        
        # Get Firestore database
        db = get_db()
        print("✓ Connected to Firebase")
        
        # Update the day document to point to the correct verse
        lookup_key = "30-06"
        correct_verse_id = " Philippians_2_14_15"  # Note the leading space
        
        print(f"Updating day '{lookup_key}' to point to verse '{correct_verse_id}'...")
        
        day_ref = db.collection('days').document(lookup_key)
        
        # Update the default mapping
        day_ref.update({
            'default.verseId': correct_verse_id
        })
        
        print("✓ Successfully updated the day mapping!")
        
        # Verify the fix
        print("\nVerifying the fix...")
        day_doc = day_ref.get()
        if day_doc.exists:
            day_data = day_doc.to_dict()
            print(f"Updated document data: {day_data}")
            
            # Test that the verse can now be retrieved
            verse_id = day_data['default'].get('verseId')
            if verse_id:
                verse_ref = db.collection('verses').document(verse_id)
                verse_doc = verse_ref.get()
                
                if verse_doc.exists:
                    print("✓ Verse document now exists and can be retrieved!")
                    
                    # Check versions
                    versions_ref = verse_ref.collection('versions')
                    versions = list(versions_ref.stream())
                    print(f"✓ Found {len(versions)} version(s) available")
                    
                    for version_doc in versions:
                        version_data = version_doc.to_dict()
                        print(f"  - {version_doc.id}: {version_data.get('text', 'No text')[:50]}...")
                        
                else:
                    print("✗ Verse document still not found!")
            else:
                print("✗ No verse ID found in updated document!")
        else:
            print("✗ Day document not found!")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_30_06()
